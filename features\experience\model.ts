import {
  ANGOLA_PROVINCES,
  AngolaP<PERSON>ince<PERSON>ey,
  DEFAULT_PROVINCE,
} from "@/lib/angola-provinces";
import { z } from "zod";
import { mediaSchema } from "@/features/shared/model";
import HotelIcon from "@/components/HotelIcon";
import ServingIcon from "@/components/ServingIcon";
import MapIcon from "@/components/MapIcon";

export const EXPERIENCE_TYPES = {
  ALL: {
    label: "Todos",
    value: "ALL",
    Icon: undefined,
  },
  ACTIVITY: {
    label: "Atividade",
    value: "ACTIVITY",
    Icon: MapIcon,
  },
  LODGING: {
    label: "Hospedagem",
    value: "LODGING",
    Icon: HotelIcon,
  },
  RESTAURANT_BAR: {
    label: "Restaurante & Bar",
    value: "RESTAURANT_BAR",
    Icon: ServingIcon,
  },
  OTHER: {
    label: "Outro",
    value: "OTHER",
    Icon: undefined,
  },
} as const;

export type ExperienceTypeKey = keyof typeof EXPERIENCE_TYPES;
export type ExperienceTypeValue =
  (typeof EXPERIENCE_TYPES)[keyof typeof EXPERIENCE_TYPES];

export const CONTACT_TYPES = {
  WHATSAPP: {
    label: "WhatsApp",
    value: "WHATSAPP",
  },
  WEBSITE: {
    label: "Website",
    value: "WEBSITE",
  },
  EMAIL: {
    label: "E-mail",
    value: "EMAIL",
  },
  PHONE: {
    label: "Telefone",
    value: "PHONE",
  },
} as const;

export type ContactTypeKey = keyof typeof CONTACT_TYPES;
export type ContactTypeValue =
  (typeof CONTACT_TYPES)[keyof typeof CONTACT_TYPES];

export const PRICE_TYPES = {
  RELATIVE: {
    label: "Relativo",
    value: "RELATIVE",
  },
  ABSOLUTE: {
    label: "Absoluto",
    value: "ABSOLUTE",
  },
} as const;

export type PriceTypeKey = keyof typeof PRICE_TYPES;
export type PriceTypeValue = (typeof PRICE_TYPES)[keyof typeof PRICE_TYPES];

export const AVAILABILITY_STATUS = {
  AVAILABLE: {
    label: "Disponível",
    value: "AVAILABLE",
  },
  UNAVAILABLE: {
    label: "Indisponível",
    value: "UNAVAILABLE",
  },
} as const;

export type AvailabilityStatusKey = keyof typeof AVAILABILITY_STATUS;
export type AvailabilityStatusValue =
  (typeof AVAILABILITY_STATUS)[keyof typeof AVAILABILITY_STATUS];

export const COMPLAINT_TYPES = {
  SPAM: {
    label: "Spam",
    value: "SPAM",
  },
  FAKE: {
    label: "Falso",
    value: "FAKE",
  },
  DUPLICATE: {
    label: "Duplicado",
    value: "DUPLICATE",
  },
  INAPPROPRIATE: {
    label: "Inapropriado",
    value: "INAPPROPRIATE",
  },
  OTHER: {
    label: "Outro",
    value: "OTHER",
  },
} as const;

export type ComplaintTypeKey = keyof typeof COMPLAINT_TYPES;
export type ComplaintTypeValue =
  (typeof COMPLAINT_TYPES)[keyof typeof COMPLAINT_TYPES];

export const COMPLAINT_STATUS = {
  NEW: {
    label: "Novo",
    value: "NEW",
  },
  PENDING: {
    label: "Pendente",
    value: "PENDING",
  },
  APPROVED: {
    label: "Aprovado",
    value: "APPROVED",
  },
  REJECTED: {
    label: "Rejeitado",
    value: "REJECTED",
  },
} as const;

export type ComplaintStatusKey = keyof typeof COMPLAINT_STATUS;
export type ComplaintStatusValue =
  (typeof COMPLAINT_STATUS)[keyof typeof COMPLAINT_STATUS];

// Provider schemas
export const providerContactSchema = z.object({
  type: z.enum(
    Object.keys(CONTACT_TYPES) as [ContactTypeKey, ...[ContactTypeKey]]
  ),
  value: z
    .string()
    .min(2, "Valor do contato deve ter pelo menos 2 caracteres")
    .max(500, "Valor do contato deve ter no máximo 500 caracteres")
    .trim(),
});

export const providerSchema = z.object({
  _id: z.string(),
  _createdAt: z.iso.datetime().optional().nullable(),
  _updatedAt: z.iso.datetime().optional().nullable(),
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres")
    .trim(),
  description: z
    .string()
    .min(10, "Descrição deve ter pelo menos 10 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim()
    .optional()
    .nullable(),
  contacts: z
    .array(providerContactSchema)
    .min(1, "Pelo menos um contato é obrigatório"),
});

// Experience schemas
export const experienceLocationSchema = z.object({
  name: z
    .string()
    .min(2, "Nome do local deve ter pelo menos 2 caracteres")
    .max(100, "Nome do local deve ter no máximo 100 caracteres")
    .trim(),
  province: z
    .enum(
      Object.keys(ANGOLA_PROVINCES) as [
        AngolaProvinceKey,
        ...AngolaProvinceKey[]
      ]
    )
    .default(DEFAULT_PROVINCE),
  mapLink: z.url("Link deve ser uma URL válida").optional().nullable(),
});

export const experienceFeatureSchema = z.object({
  name: z
    .string()
    .min(2, "Nome da característica deve ter pelo menos 2 caracteres")
    .max(100, "Nome da característica deve ter no máximo 100 caracteres")
    .trim(),
  description: z
    .string()
    .min(2, "Descrição deve ter pelo menos 2 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim()
    .optional()
    .nullable(),
});

export const experienceDetailSchema = z.object({
  title: z
    .string()
    .min(2, "Título deve ter pelo menos 2 caracteres")
    .max(100, "Título deve ter no máximo 100 caracteres")
    .trim(),
  description: z
    .string()
    .min(2, "Descrição deve ter pelo menos 2 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim()
    .optional()
    .nullable(),
});

export const experiencePriceSchema = z.object({
  type: z.enum(Object.keys(PRICE_TYPES) as [PriceTypeKey, ...[PriceTypeKey]]),
  price: z.number().min(0, "Preço deve ser maior ou igual a 0"),
  description: z
    .string()
    .min(2, "Descrição deve ter pelo menos 2 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim()
    .optional()
    .nullable(),
});

export const experienceCheckoutMethodSchema = z.object({
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(50, "Nome deve ter no máximo 50 caracteres")
    .trim(),
  type: z.enum(
    Object.keys(CONTACT_TYPES) as [ContactTypeKey, ...[ContactTypeKey]]
  ),
  value: z
    .string()
    .min(2, "Valor deve ter pelo menos 2 caracteres")
    .max(500, "Valor deve ter no máximo 500 caracteres")
    .trim(),
});

export const experienceAvailabilitySchema = z.object({
  startAt: z
    .string()
    .regex(
      /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i,
      "Formato deve ser HH:mm AM/PM"
    ),
  endAt: z
    .string()
    .regex(
      /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i,
      "Formato deve ser HH:mm AM/PM"
    )
    .optional()
    .nullable(),
  label: z
    .string()
    .min(2, "Rótulo deve ter pelo menos 2 caracteres")
    .max(50, "Rótulo deve ter no máximo 50 caracteres")
    .trim()
    .optional()
    .nullable(),
  status: z.enum(
    Object.keys(AVAILABILITY_STATUS) as [
      AvailabilityStatusKey,
      ...[AvailabilityStatusKey]
    ]
  ),
});

export const experienceItemSchema = z.object({
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres")
    .trim(),
  description: z
    .string()
    .min(10, "Descrição deve ter pelo menos 10 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim(),
});

export const experienceSchema = z.object({
  _id: z.string(),
  _type: z.literal("experience"),
  _createdAt: z.iso.datetime().optional().nullable(),
  _updatedAt: z.iso.datetime().optional().nullable(),
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres")
    .trim(),
  description: z
    .string()
    .min(10, "Descrição deve ter pelo menos 10 caracteres")
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim(),
  provider: providerSchema.optional().nullable(),
  medias: z
    .array(mediaSchema)
    .max(10, "Máximo de 10 imagens permitidas")
    .default([]),
  type: z.enum(
    Object.keys(EXPERIENCE_TYPES) as [ExperienceTypeKey, ...[ExperienceTypeKey]]
  ),
  locations: z
    .array(experienceLocationSchema)
    .min(1, "Pelo menos uma localização é obrigatória"),
  features: z.array(experienceFeatureSchema).optional().nullable(),
  details: z.array(experienceDetailSchema).optional().nullable(),
  prices: z.array(experiencePriceSchema).optional().nullable(),
  checkoutMethods: z
    .array(experienceCheckoutMethodSchema)
    .optional()
    .nullable(),
  highlightedUntil: z.iso.datetime().optional().nullable(),
  sponsoredUntil: z.iso.datetime().optional().nullable(),
  availability: z.array(experienceAvailabilitySchema).optional().nullable(),
  items: z.array(experienceItemSchema).optional().nullable(),
});

// Comment schema
export const commentSchema = z.object({
  _id: z.string(),
  _type: z.literal("comment"),
  _createdAt: z.iso.datetime(),
  _updatedAt: z.iso.datetime(),
  userId: z.string(),
  rating: z
    .number()
    .min(1, "Avaliação mínima é 1")
    .max(5, "Avaliação máxima é 5"),
  message: z
    .string()
    .min(2, "Mensagem deve ter pelo menos 2 caracteres")
    .max(500, "Mensagem deve ter no máximo 500 caracteres")
    .trim()
    .optional()
    .nullable(),
  experience: z.object({
    _id: z.string(),
    name: z.string(),
  }),
});

// Complaint schema
export const complaintSchema = z.object({
  _id: z.string(),
  _type: z.literal("complaint"),
  _createdAt: z.iso.datetime(),
  _updatedAt: z.iso.datetime(),
  userId: z.string(),
  type: z.enum(
    Object.keys(COMPLAINT_TYPES) as [ComplaintTypeKey, ...[ComplaintTypeKey]]
  ),
  reason: z
    .string()
    .min(2, "Motivo deve ter pelo menos 2 caracteres")
    .max(1000, "Motivo deve ter no máximo 1000 caracteres")
    .trim()
    .optional()
    .nullable(),
  status: z.enum(
    Object.keys(COMPLAINT_STATUS) as [
      ComplaintStatusKey,
      ...[ComplaintStatusKey]
    ]
  ),
  comment: z
    .object({
      _id: z.string(),
    })
    .optional()
    .nullable(),
});

// Type exports
export type ProviderType = z.infer<typeof providerSchema>;
export type ExperienceType = z.infer<typeof experienceSchema>;
export type CommentType = z.infer<typeof commentSchema>;
export type ComplaintType = z.infer<typeof complaintSchema>;
export type ExperienceLocationType = z.infer<typeof experienceLocationSchema>;
export type ExperienceFeatureType = z.infer<typeof experienceFeatureSchema>;
export type ExperienceDetailType = z.infer<typeof experienceDetailSchema>;
export type ExperiencePriceType = z.infer<typeof experiencePriceSchema>;
export type ExperienceCheckoutMethodType = z.infer<
  typeof experienceCheckoutMethodSchema
>;
export type ExperienceAvailabilityType = z.infer<
  typeof experienceAvailabilitySchema
>;
export type ExperienceItemType = z.infer<typeof experienceItemSchema>;

// Filters schema
export const experienceFiltersSchema = z.object({
  searchKey: z.string().optional(),
  type: z
    .enum(
      Object.keys(EXPERIENCE_TYPES) as [
        ExperienceTypeKey,
        ...[ExperienceTypeKey]
      ]
    )
    .optional(),
  province: z
    .enum(Object.keys(ANGOLA_PROVINCES) as [AngolaProvinceKey])
    .optional(),
  providerId: z.string().optional(),
  minPrice: z.coerce.number().min(0).optional(),
  maxPrice: z.coerce.number().min(0).optional(),
  highlightedUntil: z.stringbool().optional(),
  sponsoredUntil: z.stringbool().optional(),
  // TODO: Extract the sort by to a separate type
  sortBy: z.enum(["name", "createdAt", "updatedAt", "type"]).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
});

export type ExperienceFiltersType = z.infer<typeof experienceFiltersSchema>;

// ExperienceFavorite schema
export const experienceFavoriteSchema = z.object({
  _id: z.string(),
  _type: z.literal("experienceFavorite"),
  userId: z.string(),
  experienceId: z.string(),
});

export type ExperienceFavoriteType = z.infer<typeof experienceFavoriteSchema>;
