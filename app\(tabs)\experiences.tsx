import React, { useState } from "react";
import { Text, View } from "react-native";
import { useTranslation } from "react-i18next";
import Layout from "@/components/Layout";
import HeaderExperiencesFilter from "@/components/HeaderExperiencesFilter";
import ExperiencesTypeTabs from "@/features/experience/components/ExperiencesTypeTabs";
import Gap from "@/components/Gap";
import globalStyles from "@/lib/globalStyles";
import { ExperienceTypeKey } from "@/features/experience/model";

export default function ExperiencesScreen() {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<ExperienceTypeKey>("ALL");
  const [searchValue, setSearchValue] = useState("");

  const handleSearch = (text: string) => {
    setSearchValue(text);
    // TODO: Implement search functionality
  };

  const handleFilterPress = () => {
    // TODO: Implement filter functionality
  };

  const handleTypeSelect = (type: ExperienceType<PERSON>ey) => {
    setSelectedType(type);
    // TODO: Implement type filtering
  };

  return (
    <Layout
      title={{ text: t("common.experiences") }}
      noScroll
      showSearch
      headerFilter={
        <HeaderExperiencesFilter
          onSearch={handleSearch}
          onFilterPress={handleFilterPress}
          searchValue={searchValue}
          searchPlaceholder={t("common.search")}
        />
      }
    >
      <Gap y={globalStyles.gap.xs} />
      <ExperiencesTypeTabs
        selectedType={selectedType}
        onTypeSelect={handleTypeSelect}
      />
      <Gap y={globalStyles.gap.md} />

      {/* Temporary content - will be replaced with actual experiences list */}
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          paddingHorizontal: globalStyles.gap.md,
        }}
      >
        <Text
          style={{
            fontSize: globalStyles.size["4xl"],
            color: globalStyles.colors.dark.primary,
            textAlign: "center",
            marginBottom: globalStyles.gap.sm,
          }}
        >
          {t("common.experiences")}
        </Text>
        <Text
          style={{
            fontSize: globalStyles.size.xl,
            color: globalStyles.colors.light.secondary,
            textAlign: "center",
          }}
        >
          Coming soon...
        </Text>
      </View>
    </Layout>
  );
}
