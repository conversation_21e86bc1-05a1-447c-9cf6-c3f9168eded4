import React from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { cn } from "@/lib/utils";
import {
  EXPERIENCE_TYPES,
  ExperienceTypeKey,
} from "@/features/experience/model";
import globalStyles from "@/lib/globalStyles";

const experienceTypes = Object.values(EXPERIENCE_TYPES);

type ExperiencesTypeTabsProps = {
  selectedType?: ExperienceTypeKey | "ALL";
  onTypeSelect?: (type: ExperienceTypeKey | "ALL") => void;
};

const ExperiencesTypeTabs = ({
  selectedType = "ALL",
  onTypeSelect,
}: ExperiencesTypeTabsProps) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        paddingHorizontal: 16,
        gap: 8,
      }}
      className="flex-grow-0"
    >
      {experienceTypes.map(({ label, value, Icon }) => {
        const isSelected = selectedType === value;

        return (
          <TouchableOpacity
            key={value}
            onPress={() => onTypeSelect?.(value)}
            className={cn(
              "flex-row items-center px-4 py-1 rounded-full",
              isSelected ? "bg-primary-1" : "bg-light-primary"
            )}
          >
            {Icon && (
              <View className="mr-2">
                <Icon
                  className="text-light-secondary"
                  fill={
                    isSelected ? "white" : globalStyles.colors.light.secondary
                  }
                />
              </View>
            )}
            <Text
              className={cn(
                "text-sm font-medium",
                isSelected ? "text-white" : "text-light-secondary"
              )}
            >
              {label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

export default ExperiencesTypeTabs;
