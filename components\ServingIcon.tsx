import Svg, { SvgProps, G, <PERSON>, Defs, ClipPath } from "react-native-svg";

interface ServingIconProps extends SvgProps {
  className?: string;
  size?: number;
}

const ServingIcon = ({
  className,
  size = 24,
  width = size,
  height = size,
  ...props
}: ServingIconProps) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 18 18"
    fill="none"
    className={className}
    {...props}
  >
    <G clipPath="url(#a)">
      <Path
        fill="currentColor"
        fillRule="evenodd"
        d="m5.394 16.224-.172-4.316 1.941-.077c.83.114 1.669.528 2.512 1.013l1.508-.06c.684.015 1.069.692.423 1.172-.513.409-1.211.415-1.93.378-.493-.004-.489.*************.007.372-.042.54-.05.892-.036 1.62-.236 2.042-.96l.205-.535 2.199-1.202c1.107-.414 1.853.63 1.06 1.48-1.579 1.247-3.106 2.383-4.78 3.236-1.206.801-2.446.826-3.711.148l-1.863-.867ZM4.356 9.648h13.462c.1 0 .182.083.182.182v.792a.183.183 0 0 1-.182.182H4.356a.183.183 0 0 1-.181-.182V9.83c0-.1.082-.182.181-.182Zm7.254-7.58c3.686.295 6.79 3.393 6.331 7.04H4.235c-.453-3.67 2.663-6.773 6.375-7.044V1.47h-.586a.189.189 0 0 1-.188-.188v-.67c0-.103.084-.188.188-.188h2.156c.103 0 .188.085.188.187v.671a.189.189 0 0 1-.188.188h-.57v.599ZM0 11.76l4.622-.243.205 5.164-4.622.241L0 11.76Z"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h18v18H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default ServingIcon;
