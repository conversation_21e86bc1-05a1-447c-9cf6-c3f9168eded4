import { cssInterop } from "nativewind";
import Svg, {
  Circle,
  Ellipse,
  G,
  Text,
  TSpan,
  TextPath,
  Path,
  Polygon,
  Polyline,
  Line,
  Rect,
  Use,
  Image,
  Symbol,
  Defs,
  LinearGradient,
  RadialGradient,
  Stop,
  ClipPath,
  Pat<PERSON>,
  Mask,
  Marker,
  ForeignObject,
} from "react-native-svg";

// Enable NativeWind className support for react-native-svg components
// This allows SVG components to accept className prop and apply NativeWind styles

// @ts-ignore - NativeWind v4 cssInterop types may not be fully compatible with react-native-svg
cssInterop(Svg, { className: "style" });
// @ts-ignore
cssInterop(G, { className: "style" });
// @ts-ignore
cssInterop(Path, { className: "style" });
// @ts-ignore
cssInterop(Circle, { className: "style" });
// @ts-ignore
cssInterop(Rect, { className: "style" });
// @ts-ignore
cssInterop(Ellipse, { className: "style" });
// @ts-ignore
cssInterop(Line, { className: "style" });
// @ts-ignore
cssInterop(Polyline, { className: "style" });
// @ts-ignore
cssInterop(Polygon, { className: "style" });
// @ts-ignore
cssInterop(Text, { className: "style" });
